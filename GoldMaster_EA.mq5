//+------------------------------------------------------------------+
//|                                                  GoldMaster_EA.mq5 |
//|                                    Comprehensive Gold Trading EA |
//|                        Combines best practices from research     |
//+------------------------------------------------------------------+
#property copyright "GoldMaster EA - Research-Based Gold Trading System"
#property link      ""
#property version   "1.00"
#property strict

//--- Include necessary libraries
#include <Trade\Trade.mqh>
#include <Trade\SymbolInfo.mqh>
#include <Trade\PositionInfo.mqh>
#include <Trade\AccountInfo.mqh>

//--- Input parameters
input group "=== TRADING STRATEGY ==="
input bool     EnableORB = true;                    // Enable Open Range Breakout
input bool     EnableTrendFollowing = true;         // Enable Trend Following Strategy
input bool     EnableMeanReversion = false;         // Enable Mean Reversion Strategy
input int      ORB_Period = 3;                      // ORB: Minimum candles for range confirmation
input int      MA_Fast = 50;                        // Moving Average Fast Period
input int      MA_Slow = 200;                       // Moving Average Slow Period
input int      RSI_Period = 14;                     // RSI Period
input int      RSI_Overbought = 70;                 // RSI Overbought Level
input int      RSI_Oversold = 30;                   // RSI Oversold Level

input group "=== RISK MANAGEMENT ==="
input double   RiskPercentage = 1.0;                // Risk per trade (% of account)
input double   MaxDrawdownPercent = 10.0;           // Maximum drawdown (% of account)
input int      StopLossPips = 400;                  // Stop Loss in pips
input int      TakeProfitPips = 1200;               // Take Profit in pips
input double   RiskRewardRatio = 3.0;               // Risk:Reward Ratio
input bool     UseTrailingStop = true;              // Enable Trailing Stop
input int      TrailingStopPips = 100;              // Trailing Stop Distance
input int      TrailingStepPips = 50;               // Trailing Step

input group "=== TRADE MANAGEMENT ==="
input int      MaxTradesPerDay = 2;                 // Maximum trades per day
input bool     AllowLongTrades = true;              // Allow Long positions
input bool     AllowShortTrades = true;             // Allow Short positions
input int      MagicNumber = 123456;                // Magic Number
input string   TradeComment = "GoldMaster_EA";      // Trade Comment

input group "=== TIME FILTERS ==="
input int      StartTradingHour = 1;                // Start trading hour (server time)
input int      StopTradingHour = 23;                // Stop trading hour (server time)
input bool     TradeOnMonday = true;                // Trade on Monday
input bool     TradeOnFriday = true;                // Trade on Friday

//--- Global variables
CTrade         trade;
CSymbolInfo    symbolInfo;
CPositionInfo  positionInfo;
CAccountInfo   accountInfo;

datetime       lastBarTime = 0;
double         dailyStartBalance = 0;
int            dailyTradeCount = 0;
datetime       lastTradeDate = 0;

// ORB variables
double         orbHigh = 0;
double         orbLow = 0;
int            orbCandleCount = 0;
bool           orbRangeSet = false;
datetime       marketOpenTime = 0;

// Indicator handles
int            handleMA_Fast, handleMA_Slow, handleRSI;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit()
{
    // Initialize symbol info
    if(!symbolInfo.Name(_Symbol))
    {
        Print("Error initializing symbol info for ", _Symbol);
        return INIT_FAILED;
    }

    // Refresh symbol data to ensure prices are available
    if(!symbolInfo.RefreshRates())
    {
        Print("Warning: Could not refresh rates for ", _Symbol);
    }

    // Additional symbol validation
    Print("Symbol initialized: ", _Symbol);
    Print("Current Ask: ", symbolInfo.Ask());
    Print("Current Bid: ", symbolInfo.Bid());
    Print("Point: ", symbolInfo.Point());
    Print("Digits: ", symbolInfo.Digits());
    
    // Set trade parameters
    trade.SetExpertMagicNumber(MagicNumber);
    trade.SetMarginMode();
    trade.SetTypeFillingBySymbol(_Symbol);
    
    // Initialize indicators
    handleMA_Fast = iMA(_Symbol, PERIOD_H1, MA_Fast, 0, MODE_EMA, PRICE_CLOSE);
    handleMA_Slow = iMA(_Symbol, PERIOD_H1, MA_Slow, 0, MODE_EMA, PRICE_CLOSE);
    handleRSI = iRSI(_Symbol, PERIOD_H1, RSI_Period, PRICE_CLOSE);
    
    if(handleMA_Fast == INVALID_HANDLE || handleMA_Slow == INVALID_HANDLE || handleRSI == INVALID_HANDLE)
    {
        Print("Error creating indicator handles");
        return INIT_FAILED;
    }
    
    // Initialize daily tracking
    dailyStartBalance = accountInfo.Balance();
    lastTradeDate = TimeCurrent();

    // Set timer for display updates (every 30 seconds)
    EventSetTimer(30);

    Print("GoldMaster EA initialized successfully for ", _Symbol);
    Print("Strategies enabled: ORB=", EnableORB, " TrendFollowing=", EnableTrendFollowing, " MeanReversion=", EnableMeanReversion);
    Print("Risk per trade: ", RiskPercentage, "% | Max drawdown: ", MaxDrawdownPercent, "%");

    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                 |
//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
    // Kill timer
    EventKillTimer();

    // Release indicator handles
    IndicatorRelease(handleMA_Fast);
    IndicatorRelease(handleMA_Slow);
    IndicatorRelease(handleRSI);

    // Clear chart comment
    Comment("");

    Print("GoldMaster EA deinitialized. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick()
{
    // Check if new bar formed
    if(!IsNewBar()) return;
    
    // Update daily tracking
    UpdateDailyTracking();
    
    // Check risk management
    if(!CheckRiskManagement()) return;
    
    // Check time filters
    if(!CheckTimeFilters()) return;
    
    // Update trailing stops for existing positions
    if(UseTrailingStop)
        UpdateTrailingStops();
    
    // Ensure we have current market data
    if(!symbolInfo.RefreshRates())
    {
        Print("Warning: Could not refresh rates, skipping this tick");
        return;
    }

    // Check for trading signals
    CheckTradingSignals();
}

//+------------------------------------------------------------------+
//| Check if new bar formed                                          |
//+------------------------------------------------------------------+
bool IsNewBar()
{
    datetime currentBarTime = iTime(_Symbol, PERIOD_H1, 0);
    if(currentBarTime != lastBarTime)
    {
        lastBarTime = currentBarTime;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Update daily tracking variables                                  |
//+------------------------------------------------------------------+
void UpdateDailyTracking()
{
    MqlDateTime currentTime;
    TimeToStruct(TimeCurrent(), currentTime);
    
    MqlDateTime lastTradeTime;
    TimeToStruct(lastTradeDate, lastTradeTime);
    
    // Reset daily counters if new day
    if(currentTime.day != lastTradeTime.day)
    {
        dailyTradeCount = 0;
        dailyStartBalance = accountInfo.Balance();
        lastTradeDate = TimeCurrent();
        
        // Reset ORB variables for new day
        orbRangeSet = false;
        orbCandleCount = 0;
        orbHigh = 0;
        orbLow = 0;
    }
}

//+------------------------------------------------------------------+
//| Check risk management conditions                                 |
//+------------------------------------------------------------------+
bool CheckRiskManagement()
{
    // Check maximum drawdown
    double currentBalance = accountInfo.Balance();
    double drawdownPercent = ((dailyStartBalance - currentBalance) / dailyStartBalance) * 100;
    
    if(drawdownPercent > MaxDrawdownPercent)
    {
        Print("Maximum drawdown exceeded: ", drawdownPercent, "%");
        return false;
    }
    
    // Check daily trade limit
    if(dailyTradeCount >= MaxTradesPerDay)
    {
        return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Check time filters                                               |
//+------------------------------------------------------------------+
bool CheckTimeFilters()
{
    MqlDateTime currentTime;
    TimeToStruct(TimeCurrent(), currentTime);
    
    // Check trading hours
    if(currentTime.hour < StartTradingHour || currentTime.hour >= StopTradingHour)
        return false;
    
    // Check day of week
    if(currentTime.day_of_week == 1 && !TradeOnMonday) return false;
    if(currentTime.day_of_week == 5 && !TradeOnFriday) return false;
    
    return true;
}

//+------------------------------------------------------------------+
//| Update trailing stops for existing positions                     |
//+------------------------------------------------------------------+
void UpdateTrailingStops()
{
    for(int i = PositionsTotal() - 1; i >= 0; i--)
    {
        if(!positionInfo.SelectByIndex(i)) continue;
        if(positionInfo.Symbol() != _Symbol) continue;
        if(positionInfo.Magic() != MagicNumber) continue;
        
        double currentPrice = positionInfo.PriceCurrent();
        double openPrice = positionInfo.PriceOpen();
        double currentSL = positionInfo.StopLoss();
        
        if(positionInfo.PositionType() == POSITION_TYPE_BUY)
        {
            double newSL = currentPrice - TrailingStopPips * _Point * 10;
            if(newSL > currentSL + TrailingStepPips * _Point * 10)
            {
                trade.PositionModify(positionInfo.Ticket(), newSL, positionInfo.TakeProfit());
            }
        }
        else if(positionInfo.PositionType() == POSITION_TYPE_SELL)
        {
            double newSL = currentPrice + TrailingStopPips * _Point * 10;
            if(newSL < currentSL - TrailingStepPips * _Point * 10 || currentSL == 0)
            {
                trade.PositionModify(positionInfo.Ticket(), newSL, positionInfo.TakeProfit());
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Check for trading signals                                        |
//+------------------------------------------------------------------+
void CheckTradingSignals()
{
    // Get current market data
    double ask = symbolInfo.Ask();
    double bid = symbolInfo.Bid();
    double currentPrice = (ask + bid) / 2;

    // Check if we already have positions
    bool hasLongPosition = false;
    bool hasShortPosition = false;

    for(int i = 0; i < PositionsTotal(); i++)
    {
        if(!positionInfo.SelectByIndex(i)) continue;
        if(positionInfo.Symbol() != _Symbol) continue;
        if(positionInfo.Magic() != MagicNumber) continue;

        if(positionInfo.PositionType() == POSITION_TYPE_BUY)
            hasLongPosition = true;
        else if(positionInfo.PositionType() == POSITION_TYPE_SELL)
            hasShortPosition = true;
    }

    // Get indicator values
    double maFast[], maSlow[], rsi[];
    ArraySetAsSeries(maFast, true);
    ArraySetAsSeries(maSlow, true);
    ArraySetAsSeries(rsi, true);

    if(CopyBuffer(handleMA_Fast, 0, 0, 3, maFast) < 3 ||
       CopyBuffer(handleMA_Slow, 0, 0, 3, maSlow) < 3 ||
       CopyBuffer(handleRSI, 0, 0, 3, rsi) < 3)
    {
        Print("Error copying indicator buffers");
        return;
    }

    // Strategy 1: Open Range Breakout
    if(EnableORB)
    {
        int orbSignal = CheckORBSignal();
        if(orbSignal == 1 && AllowLongTrades && !hasLongPosition)
        {
            OpenTrade(ORDER_TYPE_BUY, "ORB Breakout");
        }
        else if(orbSignal == -1 && AllowShortTrades && !hasShortPosition)
        {
            OpenTrade(ORDER_TYPE_SELL, "ORB Breakdown");
        }
    }

    // Strategy 2: Trend Following
    if(EnableTrendFollowing)
    {
        int trendSignal = CheckTrendFollowingSignal(maFast, maSlow, rsi);
        if(trendSignal == 1 && AllowLongTrades && !hasLongPosition)
        {
            OpenTrade(ORDER_TYPE_BUY, "Trend Following Long");
        }
        else if(trendSignal == -1 && AllowShortTrades && !hasShortPosition)
        {
            OpenTrade(ORDER_TYPE_SELL, "Trend Following Short");
        }
    }

    // Strategy 3: Mean Reversion
    if(EnableMeanReversion)
    {
        int meanReversionSignal = CheckMeanReversionSignal(rsi);
        if(meanReversionSignal == 1 && AllowLongTrades && !hasLongPosition)
        {
            OpenTrade(ORDER_TYPE_BUY, "Mean Reversion Long");
        }
        else if(meanReversionSignal == -1 && AllowShortTrades && !hasShortPosition)
        {
            OpenTrade(ORDER_TYPE_SELL, "Mean Reversion Short");
        }
    }
}

//+------------------------------------------------------------------+
//| Check Open Range Breakout signal                                 |
//+------------------------------------------------------------------+
int CheckORBSignal()
{
    MqlDateTime currentTime;
    TimeToStruct(TimeCurrent(), currentTime);

    // Check if we're in the first hour after market open
    if(currentTime.hour == StartTradingHour && !orbRangeSet)
    {
        // Get the high and low of the first candle
        double high = iHigh(_Symbol, PERIOD_H1, 1);
        double low = iLow(_Symbol, PERIOD_H1, 1);

        if(orbCandleCount == 0)
        {
            orbHigh = high;
            orbLow = low;
            orbCandleCount = 1;
        }
        else
        {
            // Update range if new high/low
            if(high > orbHigh) orbHigh = high;
            if(low < orbLow) orbLow = low;
            orbCandleCount++;

            // Set range as final after minimum candles
            if(orbCandleCount >= ORB_Period)
            {
                orbRangeSet = true;
                Print("ORB Range Set: High=", orbHigh, " Low=", orbLow);
            }
        }
    }

    // Check for breakout/breakdown
    if(orbRangeSet)
    {
        double currentPrice = (symbolInfo.Ask() + symbolInfo.Bid()) / 2;

        if(currentPrice > orbHigh)
        {
            Print("ORB Breakout detected at ", currentPrice);
            return 1; // Buy signal
        }
        else if(currentPrice < orbLow)
        {
            Print("ORB Breakdown detected at ", currentPrice);
            return -1; // Sell signal
        }
    }

    return 0; // No signal
}

//+------------------------------------------------------------------+
//| Check Trend Following signal                                     |
//+------------------------------------------------------------------+
int CheckTrendFollowingSignal(double &maFast[], double &maSlow[], double &rsi[])
{
    // Trend following: Fast MA above Slow MA + RSI confirmation
    bool uptrend = maFast[0] > maSlow[0] && maFast[1] > maSlow[1];
    bool downtrend = maFast[0] < maSlow[0] && maFast[1] < maSlow[1];

    // MA crossover signals
    bool bullishCrossover = maFast[1] <= maSlow[1] && maFast[0] > maSlow[0];
    bool bearishCrossover = maFast[1] >= maSlow[1] && maFast[0] < maSlow[0];

    // RSI confirmation
    bool rsiNotOverbought = rsi[0] < RSI_Overbought;
    bool rsiNotOversold = rsi[0] > RSI_Oversold;

    if(bullishCrossover && rsiNotOverbought)
    {
        Print("Trend Following Long Signal: MA Crossover + RSI=", rsi[0]);
        return 1;
    }
    else if(bearishCrossover && rsiNotOversold)
    {
        Print("Trend Following Short Signal: MA Crossover + RSI=", rsi[0]);
        return -1;
    }

    return 0;
}

//+------------------------------------------------------------------+
//| Check Mean Reversion signal                                      |
//+------------------------------------------------------------------+
int CheckMeanReversionSignal(double &rsi[])
{
    // Mean reversion based on RSI extremes
    if(rsi[0] < RSI_Oversold && rsi[1] >= RSI_Oversold)
    {
        Print("Mean Reversion Long Signal: RSI oversold=", rsi[0]);
        return 1;
    }
    else if(rsi[0] > RSI_Overbought && rsi[1] <= RSI_Overbought)
    {
        Print("Mean Reversion Short Signal: RSI overbought=", rsi[0]);
        return -1;
    }

    return 0;
}

//+------------------------------------------------------------------+
//| Open a trade with proper risk management                         |
//+------------------------------------------------------------------+
void OpenTrade(ENUM_ORDER_TYPE orderType, string signalType)
{
    // Calculate position size based on risk percentage
    double lotSize = CalculatePositionSize();
    if(lotSize <= 0 || lotSize < symbolInfo.LotsMin())
    {
        Print("Invalid lot size calculated: ", lotSize, " (Min: ", symbolInfo.LotsMin(), ")");
        return;
    }

    // Get current prices - use different methods for backtesting vs live trading
    double price = 0;

    if(orderType == ORDER_TYPE_BUY)
    {
        price = symbolInfo.Ask();
        // Fallback for backtesting when Ask() returns 0
        if(price <= 0)
        {
            price = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
            if(price <= 0)
            {
                // Last resort: use current close price + spread
                price = iClose(_Symbol, PERIOD_H1, 0);
                double spread = symbolInfo.Spread() * symbolInfo.Point();
                price += spread;
            }
        }
    }
    else if(orderType == ORDER_TYPE_SELL)
    {
        price = symbolInfo.Bid();
        // Fallback for backtesting when Bid() returns 0
        if(price <= 0)
        {
            price = SymbolInfoDouble(_Symbol, SYMBOL_BID);
            if(price <= 0)
            {
                // Last resort: use current close price
                price = iClose(_Symbol, PERIOD_H1, 0);
            }
        }
    }

    // Final validation
    if(price <= 0)
    {
        Print("Invalid price after all attempts: ", price, " OrderType: ", EnumToString(orderType));
        Print("Ask: ", symbolInfo.Ask(), " Bid: ", symbolInfo.Bid());
        Print("Close[0]: ", iClose(_Symbol, PERIOD_H1, 0));
        return;
    }

    Print("Debug: Using price ", price, " for ", EnumToString(orderType), " order");

    // Calculate stop loss and take profit
    double stopLoss = 0, takeProfit = 0;
    CalculateStopLossAndTakeProfit(orderType, price, stopLoss, takeProfit);

    // Validate stop levels
    if(stopLoss <= 0 || takeProfit <= 0)
    {
        Print("Invalid stop levels: SL=", stopLoss, " TP=", takeProfit);
        return;
    }

    // Additional validation for stop levels
    double minStopLevel = symbolInfo.StopsLevel() * symbolInfo.Point();
    bool validStops = true;

    if(orderType == ORDER_TYPE_BUY)
    {
        if(stopLoss >= price || takeProfit <= price)
            validStops = false;
        if(price - stopLoss < minStopLevel || takeProfit - price < minStopLevel)
            validStops = false;
    }
    else if(orderType == ORDER_TYPE_SELL)
    {
        if(stopLoss <= price || takeProfit >= price)
            validStops = false;
        if(stopLoss - price < minStopLevel || price - takeProfit < minStopLevel)
            validStops = false;
    }

    if(!validStops)
    {
        Print("Invalid stop levels detected. Price=", price, " SL=", stopLoss, " TP=", takeProfit, " MinLevel=", minStopLevel);
        return;
    }

    // Prepare trade comment
    string comment = TradeComment + " - " + signalType;

    // Execute the trade
    bool result = false;
    if(orderType == ORDER_TYPE_BUY)
    {
        result = trade.Buy(lotSize, _Symbol, price, stopLoss, takeProfit, comment);
    }
    else if(orderType == ORDER_TYPE_SELL)
    {
        result = trade.Sell(lotSize, _Symbol, price, stopLoss, takeProfit, comment);
    }

    if(result)
    {
        dailyTradeCount++;
        Print("Trade opened successfully: ", signalType,
              " | Lot: ", lotSize,
              " | Price: ", price,
              " | SL: ", stopLoss,
              " | TP: ", takeProfit);
    }
    else
    {
        Print("Failed to open trade: ", signalType, " | Error: ", GetLastError());
        Print("Trade details: OrderType=", EnumToString(orderType), " Lot=", lotSize, " Price=", price, " SL=", stopLoss, " TP=", takeProfit);
    }
}

//+------------------------------------------------------------------+
//| Calculate position size based on risk percentage                 |
//+------------------------------------------------------------------+
double CalculatePositionSize()
{
    double accountBalance = accountInfo.Balance();
    double riskAmount = accountBalance * (RiskPercentage / 100.0);

    // For gold, calculate pip value properly
    // Gold pip value = contract size * tick size * account currency conversion
    double tickValue = symbolInfo.TickValue();
    double tickSize = symbolInfo.TickSize();

    // For gold, 1 pip = 0.1 (10 ticks if tick size is 0.01)
    double pipValue = tickValue * 10; // Convert tick value to pip value for gold

    // Calculate lot size based on stop loss
    double stopLossInPips = StopLossPips;
    double lotSize = riskAmount / (stopLossInPips * pipValue);

    // Get lot constraints
    double minLot = symbolInfo.LotsMin();
    double maxLot = symbolInfo.LotsMax();
    double lotStep = symbolInfo.LotsStep();

    // Apply constraints
    lotSize = MathMax(minLot, MathMin(maxLot, lotSize));

    // Round to lot step
    if(lotStep > 0)
        lotSize = NormalizeDouble(MathRound(lotSize / lotStep) * lotStep, 2);

    Print("Debug Position Sizing: Balance=", accountBalance, " Risk=", riskAmount,
          " PipValue=", pipValue, " LotSize=", lotSize);

    return lotSize;
}

//+------------------------------------------------------------------+
//| Calculate stop loss and take profit levels                       |
//+------------------------------------------------------------------+
void CalculateStopLossAndTakeProfit(ENUM_ORDER_TYPE orderType, double price, double &stopLoss, double &takeProfit)
{
    // Get proper point value for gold
    double point = symbolInfo.Point();

    // For gold (XAU/USD), point is typically 0.01, so we need to convert pips properly
    // 1 pip for gold = 0.1 (not 0.01 like forex pairs)
    double pipValue = point * 10; // This converts pips to actual price difference for gold

    Print("Debug: Price=", price, " Point=", point, " PipValue=", pipValue, " Digits=", symbolInfo.Digits());

    if(orderType == ORDER_TYPE_BUY)
    {
        stopLoss = price - (StopLossPips * pipValue);

        // Use fixed take profit first, then apply risk-reward if needed
        if(TakeProfitPips > 0)
        {
            takeProfit = price + (TakeProfitPips * pipValue);
        }
        else
        {
            // Calculate take profit based on risk-reward ratio
            double stopLossDistance = price - stopLoss;
            takeProfit = price + (stopLossDistance * RiskRewardRatio);
        }
    }
    else if(orderType == ORDER_TYPE_SELL)
    {
        stopLoss = price + (StopLossPips * pipValue);

        // Use fixed take profit first, then apply risk-reward if needed
        if(TakeProfitPips > 0)
        {
            takeProfit = price - (TakeProfitPips * pipValue);
        }
        else
        {
            // Calculate take profit based on risk-reward ratio
            double stopLossDistance = stopLoss - price;
            takeProfit = price - (stopLossDistance * RiskRewardRatio);
        }
    }

    // Normalize prices to proper digits
    stopLoss = NormalizeDouble(stopLoss, symbolInfo.Digits());
    takeProfit = NormalizeDouble(takeProfit, symbolInfo.Digits());

    // Validate stop levels
    double minStopLevel = symbolInfo.StopsLevel() * symbolInfo.Point();

    if(orderType == ORDER_TYPE_BUY)
    {
        if(stopLoss > price - minStopLevel)
        {
            stopLoss = price - minStopLevel;
            Print("Warning: Stop loss adjusted to minimum level: ", stopLoss);
        }
        if(takeProfit < price + minStopLevel)
        {
            takeProfit = price + minStopLevel;
            Print("Warning: Take profit adjusted to minimum level: ", takeProfit);
        }
    }
    else if(orderType == ORDER_TYPE_SELL)
    {
        if(stopLoss < price + minStopLevel)
        {
            stopLoss = price + minStopLevel;
            Print("Warning: Stop loss adjusted to minimum level: ", stopLoss);
        }
        if(takeProfit > price - minStopLevel)
        {
            takeProfit = price - minStopLevel;
            Print("Warning: Take profit adjusted to minimum level: ", takeProfit);
        }
    }

    Print("Debug: Final SL=", stopLoss, " TP=", takeProfit, " MinStopLevel=", minStopLevel);
}

//+------------------------------------------------------------------+
//| Get account statistics for display                               |
//+------------------------------------------------------------------+
string GetAccountStats()
{
    string stats = "";
    stats += "Account Balance: " + DoubleToString(accountInfo.Balance(), 2) + " " + accountInfo.Currency() + "\n";
    stats += "Account Equity: " + DoubleToString(accountInfo.Equity(), 2) + " " + accountInfo.Currency() + "\n";
    stats += "Daily Trades: " + IntegerToString(dailyTradeCount) + "/" + IntegerToString(MaxTradesPerDay) + "\n";

    double drawdown = ((dailyStartBalance - accountInfo.Balance()) / dailyStartBalance) * 100;
    stats += "Daily Drawdown: " + DoubleToString(drawdown, 2) + "%\n";

    return stats;
}

//+------------------------------------------------------------------+
//| Display information on chart                                     |
//+------------------------------------------------------------------+
void DisplayInfo()
{
    string info = "=== GoldMaster EA ===\n";
    info += GetAccountStats();
    info += "\n=== Strategy Status ===\n";
    info += "ORB: " + (EnableORB ? "ON" : "OFF") + "\n";
    info += "Trend Following: " + (EnableTrendFollowing ? "ON" : "OFF") + "\n";
    info += "Mean Reversion: " + (EnableMeanReversion ? "ON" : "OFF") + "\n";

    if(orbRangeSet)
    {
        info += "\n=== ORB Range ===\n";
        info += "High: " + DoubleToString(orbHigh, symbolInfo.Digits()) + "\n";
        info += "Low: " + DoubleToString(orbLow, symbolInfo.Digits()) + "\n";
    }

    Comment(info);
}

//+------------------------------------------------------------------+
//| Timer function for periodic updates                              |
//+------------------------------------------------------------------+
void OnTimer()
{
    DisplayInfo();
}

//+------------------------------------------------------------------+
//| Trade transaction function                                        |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                       const MqlTradeRequest& request,
                       const MqlTradeResult& result)
{
    // Log trade transactions
    if(trans.type == TRADE_TRANSACTION_DEAL_ADD)
    {
        Print("Trade transaction: Deal added - ", trans.symbol, " | Volume: ", trans.volume);
    }
}
